/* PopHub - Modern Gen-Z UI Styles */

:root {
  /* Color Palette - Dark theme with vibrant accents */
  --bg-primary: #0a0a0f;
  --bg-secondary: #1a1a2e;
  --bg-tertiary: #16213e;
  --accent-primary: #ff6b6b;
  --accent-secondary: #4ecdc4;
  --accent-tertiary: #45b7d1;
  --accent-purple: #9b59b6;
  --accent-orange: #f39c12;
  --text-primary: #ffffff;
  --text-secondary: #b8b8b8;
  --text-muted: #6c757d;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-glow: 0 8px 32px rgba(31, 38, 135, 0.37);
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-tertiary: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
  min-height: 100vh;
}

/* Animated Background */
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: linear-gradient(45deg, #0a0a0f, #1a1a2e, #16213e);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 107, 107, 0.1), rgba(78, 205, 196, 0.1));
  animation: float 20s infinite linear;
}

.shape-1 { width: 80px; height: 80px; top: 20%; left: 10%; animation-delay: 0s; }
.shape-2 { width: 120px; height: 120px; top: 60%; left: 80%; animation-delay: -5s; }
.shape-3 { width: 60px; height: 60px; top: 80%; left: 20%; animation-delay: -10s; }
.shape-4 { width: 100px; height: 100px; top: 30%; left: 70%; animation-delay: -15s; }
.shape-5 { width: 90px; height: 90px; top: 70%; left: 50%; animation-delay: -7s; }

@keyframes float {
  0% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
  50% { transform: translateY(-20px) rotate(180deg); opacity: 0.3; }
  100% { transform: translateY(0px) rotate(360deg); opacity: 0.7; }
}

/* Navigation Styles */
.custom-navbar {
  background: rgba(26, 26, 46, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.brand-logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  font-family: 'Space Grotesk', sans-serif;
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--text-primary) !important;
  transition: all 0.3s ease;
}

.brand-logo:hover {
  transform: scale(1.05);
  color: var(--accent-primary) !important;
}

.brand-icon {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-right: 0.5rem;
  font-size: 1.8rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.brand-text {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-item-custom {
  color: var(--text-secondary) !important;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.nav-item-custom:hover {
  color: var(--text-primary) !important;
  background: var(--glass-bg);
  transform: translateY(-2px);
}

.user-profile-section {
  display: flex;
  align-items: center;
}

.user-greeting {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.btn-auth {
  padding: 0.5rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.btn-login {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--accent-tertiary);
}

.btn-login:hover {
  background: var(--accent-tertiary);
  color: var(--bg-primary);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(69, 183, 209, 0.4);
}

.btn-signup {
  background: var(--gradient-secondary);
  color: white;
  border: none;
}

.btn-signup:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
}

.btn-logout {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--text-muted);
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  transition: all 0.3s ease;
}

.btn-logout:hover {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
  transform: translateY(-1px);
}

/* Main Content */
.main-content {
  padding: 2rem 0;
  min-height: calc(100vh - 100px);
}

/* Glass Card Effect */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow-glow);
  transition: all 0.3s ease;
}

.glass-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(31, 38, 135, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .brand-text {
    font-size: 1.2rem;
  }

  .user-profile-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .btn-auth {
    padding: 0.4rem 1rem;
    font-size: 0.9rem;
  }

  .main-content {
    padding: 1rem 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .search-input-group {
    min-width: 100%;
    flex-direction: column;
    padding: 1rem;
  }

  .search-type-selector {
    margin-left: 0;
    padding-left: 0;
    border-left: none;
    border-top: 1px solid var(--glass-border);
    padding-top: 1rem;
    width: 100%;
  }

  .search-wrapper {
    flex-direction: column;
    width: 100%;
  }

  .search-btn {
    width: 100%;
    justify-content: center;
  }

  .results-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .category-card {
    padding: 1.5rem 1rem;
  }

  .category-icon {
    font-size: 2.5rem;
  }

  .auth-card {
    padding: 2rem 1.5rem;
  }

  .auth-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .results-grid {
    grid-template-columns: 1fr;
  }

  .card-actions {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
  }

  .modal-content {
    width: 95%;
    max-height: 95vh;
    padding: 1.5rem;
  }

  .modal-title {
    font-size: 1.5rem;
  }

  .user-actions {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .rating-display {
    flex-direction: column;
    gap: 0.5rem;
  }

  .rating-number {
    font-size: 2rem;
  }

  .recs-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .notification {
    right: 10px;
    left: 10px;
    min-width: auto;
  }
}

/* Hero Section */
.hero-section {
  padding: 3rem 0;
  position: relative;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-title {
  font-family: 'Space Grotesk', sans-serif;
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  font-weight: 400;
  line-height: 1.6;
}

/* Search Container */
.search-container {
  margin-top: 2rem;
}

.search-wrapper {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
}

.search-input-group {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 2px solid var(--glass-border);
  border-radius: 50px;
  padding: 0.75rem 1.5rem;
  min-width: 400px;
  transition: all 0.3s ease;
}

.search-input-group:focus-within {
  border-color: var(--accent-primary);
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.3);
  transform: translateY(-2px);
}

.search-icon {
  color: var(--text-secondary);
  margin-right: 1rem;
  font-size: 1.1rem;
}

.search-input {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 1rem;
  outline: none;
  padding: 0.5rem 0;
}

.search-input::placeholder {
  color: var(--text-muted);
}

.search-type-selector {
  margin-left: 1rem;
  padding-left: 1rem;
  border-left: 1px solid var(--glass-border);
}

.type-select {
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 0.9rem;
  outline: none;
  cursor: pointer;
  padding: 0.25rem;
}

.type-select option {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.search-btn {
  background: var(--gradient-secondary);
  border: none;
  border-radius: 50px;
  padding: 0.75rem 2rem;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.search-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(245, 87, 108, 0.4);
}

/* Quick Categories */
.quick-categories {
  margin: 4rem 0;
}

.category-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2.5rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.5s cubic-bezier(0.23, 1, 0.320, 1);
  height: 100%;
  position: relative;
  overflow: hidden;
  transform-style: preserve-3d;
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: -1;
}

.category-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(255, 107, 107, 0.1), transparent, rgba(78, 205, 196, 0.1), transparent);
  opacity: 0;
  transition: all 0.8s ease;
  animation: rotate 20s linear infinite;
  z-index: -1;
}

.category-card:hover::before {
  opacity: 1;
}

.category-card:hover::after {
  opacity: 0.3;
}

.category-card:hover {
  transform: translateY(-15px) rotateX(10deg) rotateY(10deg) scale(1.05);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 107, 107, 0.5);
}

.category-card[data-type="movie"]:hover {
  border-color: rgba(255, 107, 107, 0.5);
  box-shadow: 0 25px 50px rgba(255, 107, 107, 0.2);
}

.category-card[data-type="tv"]:hover {
  border-color: rgba(79, 172, 254, 0.5);
  box-shadow: 0 25px 50px rgba(79, 172, 254, 0.2);
}

.category-card[data-type="album"]:hover {
  border-color: rgba(78, 205, 196, 0.5);
  box-shadow: 0 25px 50px rgba(78, 205, 196, 0.2);
}

.category-card[data-type="game"]:hover {
  border-color: rgba(250, 112, 154, 0.5);
  box-shadow: 0 25px 50px rgba(250, 112, 154, 0.2);
}

.category-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  display: block;
  transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.category-card:hover .category-icon {
  transform: scale(1.3) rotate(15deg) translateY(-10px);
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
}

.category-card h3 {
  font-family: 'Space Grotesk', sans-serif;
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.category-card:hover h3 {
  transform: translateY(-5px);
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.category-card p {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
  opacity: 0.9;
  transition: all 0.3s ease;
}

.category-card:hover p {
  color: var(--text-primary);
  opacity: 1;
  transform: translateY(-3px);
}

/* Results Grid */
.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

/* Result Cards - Redesigned with personality */
.result-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.23, 1, 0.320, 1);
  cursor: pointer;
  position: relative;
  transform-style: preserve-3d;
}

.result-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 107, 107, 0.1) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
}

.result-card:hover::before {
  opacity: 1;
}

.result-card:hover {
  transform: translateY(-15px) rotateX(5deg) rotateY(5deg);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 107, 107, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 107, 107, 0.5);
}

.card-image-container {
  position: relative;
  height: 320px;
  overflow: hidden;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
  filter: brightness(0.9) contrast(1.1);
}

.result-card:hover .card-image {
  transform: scale(1.15) rotate(2deg);
  filter: brightness(1.1) contrast(1.2) saturate(1.3);
}

.card-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  color: white;
  font-size: 3rem;
  position: relative;
}

.card-placeholder::after {
  content: 'No Image';
  font-size: 0.9rem;
  font-weight: 500;
  margin-top: 0.5rem;
  opacity: 0.8;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.4s ease;
  backdrop-filter: blur(5px);
}

.result-card:hover .card-overlay {
  opacity: 1;
}

.quick-action-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border: none;
  border-radius: 20px;
  width: 60px;
  height: 60px;
  color: white;
  font-size: 1.4rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
  position: relative;
  overflow: hidden;
}

.quick-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.quick-action-btn:hover::before {
  left: 100%;
}

.quick-action-btn:hover {
  transform: scale(1.2) rotate(10deg);
  box-shadow: 0 15px 35px rgba(255, 107, 107, 0.5);
}

.card-content {
  padding: 2rem 1.5rem 1.5rem;
  position: relative;
}

.card-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 1.5rem;
  right: 1.5rem;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.card-type-badge {
  position: absolute;
  top: -15px;
  left: 1.5rem;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: uppercase;
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.type-icon {
  font-size: 1rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.card-title {
  font-family: 'Space Grotesk', sans-serif;
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 1rem 0 0.5rem 0;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-year {
  color: var(--text-secondary);
  font-size: 0.95rem;
  margin-bottom: 1rem;
  font-weight: 500;
  opacity: 0.8;
}

.card-genres {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.genre-tag {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
  color: var(--text-secondary);
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.genre-tag:hover {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.2) 0%, rgba(78, 205, 196, 0.2) 100%);
  border-color: rgba(255, 107, 107, 0.3);
  transform: translateY(-2px);
}

.card-actions {
  display: flex;
  gap: 0.75rem;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 16px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  font-size: 0.9rem;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.action-btn.primary:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.5);
}

.action-btn.secondary {
  background: transparent;
  color: var(--accent-secondary);
  border: 2px solid var(--accent-secondary);
  position: relative;
}

.action-btn.secondary:hover {
  background: var(--accent-secondary);
  color: var(--bg-primary);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 35px rgba(78, 205, 196, 0.4);
}

/* Additional card elements */
.card-rating-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 0.5rem 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  z-index: 2;
}

.rating-stars {
  color: #ffd700;
  font-size: 0.8rem;
}

.rating-number {
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
}

.card-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

/* Hover effects with CSS variables for tilt */
.result-card:hover {
  transform: translateY(-15px) rotateX(calc(var(--tilt-x, 0deg) + 5deg)) rotateY(calc(var(--tilt-y, 0deg) + 5deg));
}

/* Staggered animation for cards */
.result-card:nth-child(1) { animation-delay: 0.1s; }
.result-card:nth-child(2) { animation-delay: 0.2s; }
.result-card:nth-child(3) { animation-delay: 0.3s; }
.result-card:nth-child(4) { animation-delay: 0.4s; }
.result-card:nth-child(5) { animation-delay: 0.5s; }
.result-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.result-card {
  animation: cardSlideIn 0.6s ease-out forwards;
  opacity: 0;
}

/* Enhanced genre tags */
.genre-tag:nth-child(1) {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.2) 0%, rgba(255, 107, 107, 0.1) 100%);
  border-color: rgba(255, 107, 107, 0.3);
}

.genre-tag:nth-child(2) {
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.2) 0%, rgba(78, 205, 196, 0.1) 100%);
  border-color: rgba(78, 205, 196, 0.3);
}

.genre-tag:nth-child(3) {
  background: linear-gradient(135deg, rgba(79, 172, 254, 0.2) 0%, rgba(79, 172, 254, 0.1) 100%);
  border-color: rgba(79, 172, 254, 0.3);
}

/* Loading States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid var(--glass-border);
  border-top: 3px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin: 0;
}

/* No Results / Error States */
.no-results, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.no-results-icon, .error-icon {
  font-size: 4rem;
  color: var(--text-muted);
  margin-bottom: 1rem;
}

.no-results h3, .error-state h3 {
  color: var(--text-primary);
  font-family: 'Space Grotesk', sans-serif;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.no-results p, .error-state p {
  color: var(--text-secondary);
  margin: 0;
}

/* Authentication Pages */
.auth-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
  padding: 2rem 1rem;
}

.auth-card {
  width: 100%;
  max-width: 450px;
  padding: 3rem;
  text-align: center;
}

.auth-header {
  margin-bottom: 2.5rem;
}

.auth-title {
  font-family: 'Space Grotesk', sans-serif;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.auth-subtitle {
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin: 0;
}

.auth-form {
  text-align: left;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.form-label i {
  color: var(--accent-primary);
  width: 16px;
}

.auth-form input[type="text"],
.auth-form input[type="password"] {
  width: 100%;
  padding: 1rem 1.25rem;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid var(--glass-border);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.auth-form input[type="text"]:focus,
.auth-form input[type="password"]:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.2);
  transform: translateY(-2px);
}

.auth-form input::placeholder {
  color: var(--text-muted);
}

.form-error {
  color: var(--accent-primary);
  font-size: 0.85rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.form-error::before {
  content: "⚠️";
  font-size: 0.8rem;
}

.form-help {
  color: var(--text-muted);
  font-size: 0.8rem;
  margin-top: 0.5rem;
  line-height: 1.4;
}

.auth-btn {
  width: 100%;
  padding: 1rem 2rem;
  background: var(--gradient-secondary);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.auth-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(245, 87, 108, 0.4);
}

.auth-btn.signup {
  background: var(--gradient-tertiary);
}

.auth-btn.signup:hover {
  box-shadow: 0 15px 35px rgba(79, 172, 254, 0.4);
}

.auth-footer {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--glass-border);
  text-align: center;
}

.auth-footer p {
  color: var(--text-secondary);
  margin: 0;
}

.auth-link {
  color: var(--accent-primary);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.auth-link:hover {
  color: var(--accent-secondary);
  text-decoration: underline;
}

/* Detail Modal */
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.detail-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
}

.modal-content {
  position: relative;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.detail-modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--glass-border);
  margin-bottom: 2rem;
}

.modal-title-section {
  flex: 1;
}

.modal-title {
  font-family: 'Space Grotesk', sans-serif;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.modal-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.type-badge, .year-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.type-badge {
  background: var(--gradient-tertiary);
  color: white;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.year-badge {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  border: 1px solid var(--glass-border);
}

.modal-close {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: var(--accent-primary);
  color: white;
  transform: scale(1.1);
}

.modal-body {
  padding: 0;
}

.community-rating {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: center;
}

.rating-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.rating-number {
  font-size: 3rem;
  font-weight: 700;
  color: var(--accent-primary);
  font-family: 'Space Grotesk', sans-serif;
}

.rating-info {
  text-align: left;
}

.stars {
  font-size: 1.2rem;
  color: var(--accent-primary);
  margin-bottom: 0.25rem;
}

.rating-count {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.description {
  margin-bottom: 2rem;
}

.description h3 {
  font-family: 'Space Grotesk', sans-serif;
  font-size: 1.3rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.description p {
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 1rem;
}

.user-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.rating-section, .list-actions {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 1.5rem;
}

.rating-section h3, .list-actions h3 {
  font-family: 'Space Grotesk', sans-serif;
  font-size: 1.1rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.star-rating {
  display: flex;
  gap: 0.25rem;
  margin-bottom: 1rem;
  justify-content: center;
}

.star-rating .star {
  font-size: 2rem;
  color: var(--text-muted);
  cursor: pointer;
  transition: all 0.2s ease;
}

.star-rating .star:hover {
  color: var(--accent-primary);
  transform: scale(1.2);
}

.review-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--glass-border);
  border-radius: 10px;
  padding: 1rem;
  color: var(--text-primary);
  font-size: 0.9rem;
  resize: vertical;
  min-height: 80px;
  margin-bottom: 1rem;
}

.review-input:focus {
  outline: none;
  border-color: var(--accent-primary);
}

.list-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.list-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: transparent;
  border: 2px solid var(--glass-border);
  border-radius: 10px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.list-btn:hover {
  border-color: var(--accent-secondary);
  color: var(--accent-secondary);
  background: rgba(78, 205, 196, 0.1);
}

.list-btn.added {
  border-color: var(--accent-primary);
  color: var(--accent-primary);
  background: rgba(255, 107, 107, 0.1);
}

.recommendations-section {
  border-top: 1px solid var(--glass-border);
  padding-top: 2rem;
}

.recs-title {
  font-family: 'Space Grotesk', sans-serif;
  font-size: 1.3rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.recs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.rec-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.rec-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.1);
}

.rec-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.rec-placeholder {
  width: 100%;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  color: var(--text-muted);
  font-size: 2rem;
}

.rec-info {
  padding: 0.75rem;
}

.rec-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.3;
}

/* Notifications */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 10px;
  padding: 1rem 1.5rem;
  color: var(--text-primary);
  z-index: 10001;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
  min-width: 250px;
}

.notification.show {
  transform: translateX(0);
}

.notification.success {
  border-color: var(--accent-secondary);
  background: rgba(78, 205, 196, 0.1);
}

.notification.error {
  border-color: var(--accent-primary);
  background: rgba(255, 107, 107, 0.1);
}

.notification button {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  margin-left: auto;
}

/* Utility Classes */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glow-effect {
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.3);
}

.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-3px);
}

/* Typewriter Effect */
.typewriter {
  border-right: 2px solid var(--accent-primary);
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { border-color: var(--accent-primary); }
  51%, 100% { border-color: transparent; }
}

/* Custom Cursor */
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: var(--accent-primary);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: transform 0.1s ease;
  mix-blend-mode: difference;
}

.custom-cursor.hover {
  transform: scale(2);
  background: var(--accent-secondary);
}

/* Animation Classes */
.animate-in {
  animation: slideInUp 0.6s ease forwards;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
